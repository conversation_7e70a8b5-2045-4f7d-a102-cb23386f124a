# 🗂️ Multiwell Output Directory Structure

This document describes the updated output directory structure for the multiwell implementation, ensuring all generated files are contained within the multiwell directory.

## 📁 **New Output Structure**

```
multiwell/
├── training/
│   └── outputs/                    # 🆕 All training outputs
│       ├── {CURVE}_fold_{N}/       # Cross-validation fold results
│       │   ├── best_model.pth      # Trained model checkpoint
│       │   └── training_config.json # Training configuration
│       ├── data_validation_report.json
│       ├── enhanced_training_results.json
│       └── training_summary_report.md
│
├── validation/
│   └── outputs/                    # 🆕 All validation outputs
│       ├── {CURVE}_validation_results.png
│       ├── comprehensive_validation_results.json
│       └── validation_report.md
│
├── prediction/
│   └── outputs/                    # 🆕 All prediction outputs
│       └── improved_multi_curve_demo_{well}.png
│
└── demos/
    └── (no specific output directory - uses console output)
```

## 🔄 **Changes Made**

### **Training Script** (`training/enhanced_multi_curve_training.py`)
- **Before**: `results_dir = Path("enhanced_training_outputs")`
- **After**: `results_dir = script_dir / "outputs"`
- **Location**: All outputs saved in `multiwell/training/outputs/`

### **Validation Script** (`validation/model_validation_suite.py`)
- **Before**: `results_dir = Path("validation_outputs")`
- **After**: `results_dir = script_dir / "outputs"`
- **Location**: All outputs saved in `multiwell/validation/outputs/`

### **Prediction Script** (`prediction/improved_multi_curve_prediction_demo.py`)
- **Before**: Saved plots in current directory or user-selected directory
- **After**: Default saves to `multiwell/prediction/outputs/`
- **Model Search**: Updated to look in `multiwell/training/outputs/` first

## 🎯 **Benefits**

1. **🗂️ Self-Contained**: All multiwell outputs stay within multiwell directory
2. **🧹 Clean Root**: No files written to the project root directory
3. **📋 Organized**: Clear separation between training, validation, and prediction outputs
4. **🔍 Predictable**: Easy to find outputs for each component
5. **🚀 Maintainable**: Consistent structure across all multiwell scripts

## 📋 **Data File Locations**

The scripts now search for data files in this priority order:

1. **Project root** - Init_transformer/ (PRIMARY LOCATION for A1.hdf5, A2.hdf5)
2. **`multiwell/training/`** - Script directory
3. **`multiwell/`** - Multiwell root
4. **`examples/`** - Former location (fallback)
5. **`archives/old_code/root_duplicates/`** - Archived data files

## 🔧 **Model File Locations**

The validation and prediction scripts search for trained models in:

1. **`multiwell/training/outputs/`** - New training outputs (primary)
2. **`archives/old_code/output_directories/production_training_outputs/`** - Archived models
3. **`multiwell/production_training_outputs/`** - Legacy location (if exists)

## ✅ **Verification**

All scripts have been tested and verified:

- ✅ **Training**: Creates `multiwell/training/outputs/` directory
- ✅ **Validation**: Creates `multiwell/validation/outputs/` directory  
- ✅ **Prediction**: Creates `multiwell/prediction/outputs/` directory
- ✅ **Data Discovery**: Finds data files in `examples/` directory
- ✅ **Model Discovery**: Finds archived models for prediction/validation

## 🚀 **Usage Examples**

### Training
```bash
cd multiwell/training/
python enhanced_multi_curve_training.py
# Outputs saved to: multiwell/training/outputs/
```

### Validation
```bash
cd multiwell/validation/
python model_validation_suite.py
# Outputs saved to: multiwell/validation/outputs/
```

### Prediction
```bash
cd multiwell/prediction/
python improved_multi_curve_prediction_demo.py
# Outputs saved to: multiwell/prediction/outputs/
```

### Launcher (All-in-One)
```bash
cd multiwell/
python multiwell_launcher.py train    # Uses training/outputs/
python multiwell_launcher.py validate # Uses validation/outputs/
python multiwell_launcher.py predict  # Uses prediction/outputs/
```

## 📚 **Related Documentation**

- **Main Structure**: `CLEAN_STRUCTURE_README.md`
- **Archive Summary**: `archives/old_code/ARCHIVE_SUMMARY.md`
- **Multiwell Guide**: `multiwell/MULTIWELL_QUICK_START_GUIDE.md`

---

**🎉 Self-Contained Structure Complete!** All multiwell outputs now stay within the multiwell directory, maintaining clean organization and preventing root directory pollution.
