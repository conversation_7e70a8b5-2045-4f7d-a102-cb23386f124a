#!/usr/bin/env python3
"""
Enhanced Multi-Curve Training Script
Addresses Priority 1 & 2 improvements:
1. Fixed normalization infrastructure
2. Robust model training with cross-validation
3. Comprehensive validation metrics
4. Confidence interval estimation

Author: Devri
Date: 2025-08-22
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
from pathlib import Path
from sklearn.model_selection import <PERSON><PERSON><PERSON>
from typing import Dict, List, Tuple, Optional
import argparse
import logging
import torch

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from vp_predictor import VpDataNormalizer
from vp_predictor.core.dataset import GeneralWellLogDataset, create_general_dataset
from vp_predictor.core.training import GeneralTrainingManager
from vp_predictor.core.loss_functions import GeneralWellLogLoss
# Import proven VP-specific components
from vp_predictor.vp_model_improved import VpLoss
from vp_predictor.vp_model_improved import MWLT_Vp_Base
from vp_predictor.utils import get_device

# Try to import training examples, fallback to synthetic data if not available
try:
    from examples.production_training_examples import load_training_data
    TRAINING_EXAMPLES_AVAILABLE = True
except ImportError:
    TRAINING_EXAMPLES_AVAILABLE = False
    def load_real_training_data():
        """Load real training data from HDF5 files (using proven approach from initial implementation)"""
        from vp_predictor.las_processor import LASProcessor
        
        # Auto-detect data file locations (prioritize examples directory)
        script_dir = Path(__file__).parent
        project_root = script_dir.parent.parent  # Go up to Init_transformer/

        possible_paths = [
            project_root,  # Project root (primary location - where A1.hdf5 and A2.hdf5 are now located)
            script_dir,  # Current script directory
            script_dir.parent,  # multiwell/ directory
            project_root / "examples",  # examples/ directory (fallback)
            project_root / "archives" / "old_code" / "root_duplicates",  # Archived data files
        ]

        found_files = []
        for base_path in possible_paths:
            base_path = Path(base_path)
            a1_path = base_path / 'A1.hdf5'
            a2_path = base_path / 'A2.hdf5'

            if a1_path.exists() and a2_path.exists():
                found_files = [str(a1_path), str(a2_path)]
                logger.info(f"   ✅ Found data files in: {base_path.absolute()}")
                break

        if not found_files:
            logger.error("   ❌ Could not find A1.hdf5 and A2.hdf5 files!")
            # Fallback to synthetic data
            return create_synthetic_training_data()

        # Process real HDF5 data using proven approach
        processor = LASProcessor()
        all_data = {}
        sequence_length = 720
        augmentation_factor = 2.0
        step_size = int(sequence_length / augmentation_factor)

        for file_path in found_files:
            logger.info(f"   📂 Processing {file_path}...")
            curves = processor.process_hdf5_to_las_format(file_path)

            if 'AC' not in curves:
                logger.warning(f"   ⚠️  No AC curve found in {file_path}, skipping...")
                continue

            # Create overlapping windows (same as initial implementation)
            data_length = len(curves['AC'])
            num_windows = max(1, (data_length - sequence_length) // step_size + 1)

            logger.info(f"   📊 Creating {num_windows} windows from {data_length} samples")

            for i in range(num_windows):
                start_idx = i * step_size
                end_idx = start_idx + sequence_length

                if end_idx > data_length:
                    start_idx = data_length - sequence_length
                    end_idx = data_length

                # Extract window and validate quality
                window_valid = True
                for curve_name in ['GR', 'CNL', 'DEN', 'AC', 'RLLD']:
                    if curve_name in curves:
                        data = curves[curve_name][start_idx:end_idx]
                        valid_ratio = np.sum(~np.isnan(data) & (data > 0)) / len(data)
                        if valid_ratio < 0.8:  # Quality threshold
                            window_valid = False
                            break

                if not window_valid:
                    continue

                # Store valid window data
                for curve_name in ['GR', 'CNL', 'DEN', 'AC', 'RLLD']:
                    if curve_name in curves:
                        if curve_name not in all_data:
                            all_data[curve_name] = []
                        all_data[curve_name].extend(curves[curve_name][start_idx:end_idx])

        # Convert to numpy arrays
        for curve_name in all_data:
            all_data[curve_name] = np.array(all_data[curve_name])
            
        total_length = len(all_data['AC']) if 'AC' in all_data else 0
        logger.info(f"   ✅ Loaded real data with {total_length} total samples")
        
        return all_data

    def create_synthetic_training_data():
        """Create synthetic training data for demonstration (fallback only)"""
        # Keep existing synthetic data generation as fallback
        n_wells = 25  # 25 wells for cross-validation (5 wells per fold)
        seq_length_per_well = 3000  # Much longer sequences per well (ensures >720 per fold)

        np.random.seed(42)  # For reproducible results

        # Create realistic synthetic well log data with proper sequence length
        synthetic_data = {}
        
        # Generate continuous sequences for each well
        all_curves = ['GR', 'CNL', 'DEN', 'AC', 'RLLD']
        
        for curve in all_curves:
            curve_data = []
            
            for well_idx in range(n_wells):
                if curve == 'GR':
                    # Gamma ray: typically 0-200 API
                    base_trend = np.linspace(60, 120, seq_length_per_well)
                    noise = np.random.randn(seq_length_per_well) * 15
                    well_data = base_trend + noise
                    well_data = np.clip(well_data, 0, 250)
                    
                elif curve == 'CNL':
                    # Neutron porosity: typically 0-60%
                    base_trend = np.linspace(15, 35, seq_length_per_well)
                    noise = np.random.randn(seq_length_per_well) * 8
                    well_data = base_trend + noise
                    well_data = np.clip(well_data, 0, 60)
                    
                elif curve == 'DEN':
                    # Bulk density: typically 1.8-2.8 g/cc
                    base_trend = np.linspace(2.1, 2.5, seq_length_per_well)
                    noise = np.random.randn(seq_length_per_well) * 0.15
                    well_data = base_trend + noise
                    well_data = np.clip(well_data, 1.5, 3.0)
                    
                elif curve == 'AC':
                    # Acoustic transit time: typically 100-400 us/ft
                    base_trend = np.linspace(180, 240, seq_length_per_well)
                    noise = np.random.randn(seq_length_per_well) * 25
                    well_data = base_trend + noise
                    well_data = np.clip(well_data, 50, 500)
                    
                elif curve == 'RLLD':
                    # Deep resistivity: log-normal distribution
                    base_trend = np.linspace(1, 3, seq_length_per_well)  # log scale
                    noise = np.random.randn(seq_length_per_well) * 0.5
                    well_data = np.power(10, base_trend + noise)
                    well_data = np.clip(well_data, 0.1, 1000)
                
                curve_data.extend(well_data)
            
            synthetic_data[curve] = np.array(curve_data)
        
        # Add geological relationships to make it more realistic
        total_length = len(synthetic_data['GR'])
        
        # Porosity relationship: higher CNL -> lower DEN
        synthetic_data['DEN'] -= 0.008 * (synthetic_data['CNL'] - 25)
        
        # Velocity relationship: higher DEN -> lower AC (Wyllie time average)
        synthetic_data['AC'] -= 50 * (synthetic_data['DEN'] - 2.3)
        
        # Resistivity-porosity relationship (Archie's law approximation)
        porosity_proxy = synthetic_data['CNL'] / 50.0
        resistivity_factor = np.power(porosity_proxy + 0.1, -2)
        synthetic_data['RLLD'] *= resistivity_factor
        
        # Clip final values to realistic ranges
        synthetic_data['DEN'] = np.clip(synthetic_data['DEN'], 1.5, 3.0)
        synthetic_data['AC'] = np.clip(synthetic_data['AC'], 50, 500)
        synthetic_data['RLLD'] = np.clip(synthetic_data['RLLD'], 0.1, 1000)
        
        logger.info(f"   📊 Generated {n_wells} wells with {seq_length_per_well} samples each (synthetic fallback)")
        logger.info(f"   📏 Total sequence length: {total_length} samples")
        
        return synthetic_data

# Setup logging with proper file handling
import atexit

# Create file handler with proper cleanup
log_file_handler = logging.FileHandler('enhanced_training.log')
atexit.register(log_file_handler.close)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        log_file_handler,
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CurveSpecificLoss:
    """Curve-specific loss functions optimized for different well log types"""
    
    def __init__(self, target_curve: str):
        self.target_curve = target_curve
        self.mse_loss = nn.MSELoss()
        
    def __call__(self, predictions, targets):
        """Apply simplified loss calculation for better convergence"""
        if self.target_curve == 'AC':
            # Use proven VpLoss for AC 
            return VpLoss()(predictions, targets)
        else:
            # Use standard MSE for all other curves (simple and proven)
            # The normalization handles curve-specific ranges
            return self.mse_loss(predictions, targets)

class VpSpecificDataset:
    """Custom dataset that uses VP-specific normalization for AC targets"""
    
    def __init__(self, data_dict, input_curves, target_curve, normalizer, sequence_length=640):
        """
        Initialize dataset with VP-specific handling for AC targets
        
        Args:
            data_dict: Dictionary containing curve data
            input_curves: List of input curve names
            target_curve: Target curve name
            normalizer: VpDataNormalizer instance
            sequence_length: Length of sequences to create
        """
        self.data_dict = data_dict
        self.input_curves = input_curves
        self.target_curve = target_curve
        self.normalizer = normalizer
        self.sequence_length = sequence_length
        
        # Create sequences from continuous data
        self.input_sequences, self.target_sequences = self._create_sequences()
        
    def _create_sequences(self):
        """Create sequences from continuous curve data"""
        # Get the length of continuous data
        total_length = len(self.data_dict[self.target_curve])
        
        # Calculate number of sequences
        num_sequences = total_length // self.sequence_length
        
        input_sequences = []
        target_sequences = []
        
        for i in range(num_sequences):
            start_idx = i * self.sequence_length
            end_idx = start_idx + self.sequence_length
            
            # Prepare input features
            input_features = []
            for curve_name in self.input_curves:
                if curve_name in self.data_dict:
                    curve_data = self.data_dict[curve_name][start_idx:end_idx]
                    # Apply curve-specific normalization
                    curve_data = torch.FloatTensor(curve_data)
                    curve_data = torch.nan_to_num(curve_data, nan=0.0)
                    
                    if curve_name == 'RLLD':
                        # Log transform for resistivity
                        curve_data = torch.log10(torch.clamp(curve_data, min=0.1))
                        normalized = (curve_data - 1.0) / 2.0
                    else:
                        # Use normalizer statistics
                        stats = self.normalizer.input_stats.get(curve_name, {'mean': 0.0, 'std': 1.0})
                        normalized = (curve_data - stats['mean']) / stats['std']
                        normalized = torch.clamp(normalized, -3, 3) / 3
                    
                    input_features.append(normalized.numpy())
                else:
                    # Fill missing curves with zeros
                    input_features.append(np.zeros(self.sequence_length))
            
            # Prepare target (use VP-specific normalization for AC)
            target_data = self.data_dict[self.target_curve][start_idx:end_idx]
            input_sequences.append(np.array(input_features))  # Shape: [num_curves, seq_len]
            target_sequences.append(target_data)  # Keep original units for VP-specific normalization
            
        return np.array(input_sequences), np.array(target_sequences)
    
    def __len__(self):
        return len(self.input_sequences)
    
    def __getitem__(self, idx):
        """Get item with VP-specific target normalization"""
        inputs = torch.FloatTensor(self.input_sequences[idx])
        targets = torch.FloatTensor(self.target_sequences[idx])
        
        # Apply VP-specific normalization to AC targets
        if self.target_curve == 'AC':
            targets = self.normalizer.normalize_vp(targets)
        
        return inputs, targets.unsqueeze(0)  # Add channel dimension to targets

class CurveSpecificDataset:
    """Enhanced dataset with curve-specific normalization for all target types"""
    
    def __init__(self, data_dict, input_curves, target_curve, normalizer, sequence_length=640):
        """
        Initialize dataset with curve-specific handling for all targets
        
        Args:
            data_dict: Dictionary containing curve data
            input_curves: List of input curve names  
            target_curve: Target curve name
            normalizer: VpDataNormalizer instance
            sequence_length: Length of sequences to create
        """
        self.data_dict = data_dict
        self.input_curves = input_curves
        self.target_curve = target_curve
        self.normalizer = normalizer
        self.sequence_length = sequence_length
        
        # Create sequences from continuous data
        self.input_sequences, self.target_sequences = self._create_sequences()
        
        logger.info(f"   📊 Created {len(self.input_sequences)} sequences for {target_curve} prediction")
        
    def _create_sequences(self):
        """Create sequences from continuous curve data"""
        # Get the length of continuous data
        total_length = len(self.data_dict[self.target_curve])
        
        # Calculate number of sequences with aggressive overlap for more training data
        step_size = 64  # Small step size for maximum data augmentation (was 320, now 64)
        num_sequences = max(1, (total_length - self.sequence_length) // step_size + 1)
        
        input_sequences = []
        target_sequences = []
        
        for i in range(num_sequences):
            start_idx = i * step_size
            end_idx = start_idx + self.sequence_length
            
            if end_idx > total_length:
                start_idx = total_length - self.sequence_length
                end_idx = total_length
                
            # Prepare input features with curve-specific normalization
            input_features = []
            for curve_name in self.input_curves:
                if curve_name in self.data_dict:
                    curve_data = self.data_dict[curve_name][start_idx:end_idx]
                    normalized_curve = self._normalize_input_curve(curve_data, curve_name)
                    input_features.append(normalized_curve)
                else:
                    # Fill missing curves with zeros
                    input_features.append(np.zeros(self.sequence_length))
            
            # Prepare target with curve-specific handling
            target_data = self.data_dict[self.target_curve][start_idx:end_idx]
            
            # Quality check: skip sequences with too many invalid values
            if self._is_valid_sequence(input_features, target_data):
                input_sequences.append(np.array(input_features))  # Shape: [num_curves, seq_len]
                target_sequences.append(target_data)  # Keep original units for curve-specific normalization
            
        return np.array(input_sequences), np.array(target_sequences)
    
    def _normalize_input_curve(self, curve_data, curve_name):
        """Apply simplified, proven normalization to input curves"""
        # Convert to tensor and handle NaN values
        curve_data = torch.FloatTensor(curve_data)
        curve_data = torch.nan_to_num(curve_data, nan=0.0)
        
        # Special handling for resistivity (log transform is essential)
        if curve_name == 'RLLD':
            curve_data = torch.log10(torch.clamp(curve_data, min=0.1))
        
        # Simple z-score normalization for all curves (proven approach)
        mean_val = curve_data.mean()
        std_val = curve_data.std() + 1e-8  # Avoid division by zero
        normalized = (curve_data - mean_val) / std_val
        
        # Gentle clipping to prevent extreme values
        normalized = torch.clamp(normalized, -3, 3)
        
        return normalized.numpy()
    
    def _normalize_target(self, target_data):
        """Apply simplified target normalization"""
        target_tensor = torch.FloatTensor(target_data)
        
        if self.target_curve == 'AC':
            # Use VP-specific normalization (proven to work)
            return self.normalizer.normalize_vp(target_tensor)
        elif self.target_curve == 'RLLD':
            # Log transform for resistivity targets
            target_tensor = torch.log10(torch.clamp(target_tensor, min=0.1))
        
        # For all non-VP curves, use simple z-score normalization
        if self.target_curve != 'AC':
            mean_val = target_tensor.mean()
            std_val = target_tensor.std() + 1e-8
            target_tensor = (target_tensor - mean_val) / std_val
            target_tensor = torch.clamp(target_tensor, -3, 3)
        
        return target_tensor
    
    def _is_valid_sequence(self, input_features, target_data):
        """Check if sequence has sufficient valid data"""
        # Check target data quality
        valid_target_ratio = np.sum(~np.isnan(target_data) & (target_data > 0)) / len(target_data)
        if valid_target_ratio < 0.7:
            return False
            
        # Check input features quality
        for feature in input_features:
            valid_input_ratio = np.sum(~np.isnan(feature)) / len(feature)
            if valid_input_ratio < 0.7:
                return False
                
        return True
    
    def __len__(self):
        return len(self.input_sequences)
    
    def __getitem__(self, idx):
        """Get item with curve-specific target normalization"""
        inputs = torch.FloatTensor(self.input_sequences[idx])
        target_data = self.target_sequences[idx]
        
        # Apply curve-specific target normalization
        targets = self._normalize_target(target_data)
        
        return inputs, targets.unsqueeze(0)  # Add channel dimension to targets

class EnhancedTrainingPipeline:
    """Enhanced training pipeline with cross-validation and confidence metrics"""

    def __init__(self, config: Dict):
        self.config = config
        self.device = get_device()
        self.normalizer = VpDataNormalizer()

        # Set results directory within multiwell/training/outputs/
        script_dir = Path(__file__).parent
        self.results_dir = script_dir / "outputs"
        self.results_dir.mkdir(exist_ok=True)

        logger.info(f"🚀 Enhanced Training Pipeline Initialized")
        logger.info(f"📱 Device: {self.device}")
        logger.info(f"📁 Results directory: {self.results_dir}")
    
    def load_and_validate_data(self) -> Dict[str, np.ndarray]:
        """Load data with comprehensive validation"""
        logger.info("📂 Loading and validating training data...")
        
        try:
            if TRAINING_EXAMPLES_AVAILABLE:
                data_dict = load_training_data()
                logger.info("   ✅ Loaded real training data")
            else:
                logger.info("   📂 Attempting to load real HDF5 data...")
                data_dict = load_real_training_data()
                logger.info("   ✅ Loaded real HDF5 training data")
                
            logger.info(f"✅ Loaded data for curves: {list(data_dict.keys())}")
            
            # Validate data quality
            validation_report = {}
            well_length = 3000  # Expected length per well
            total_length = len(next(iter(data_dict.values())))
            n_wells = total_length // well_length
            
            for curve_name, curve_data in data_dict.items():
                # Basic statistics
                valid_samples = np.sum(np.isfinite(curve_data))
                total_samples = curve_data.size
                completeness = valid_samples / total_samples
                
                validation_report[curve_name] = {
                    'total_samples': total_samples,
                    'valid_samples': valid_samples,
                    'completeness': completeness,
                    'range': [np.nanmin(curve_data), np.nanmax(curve_data)],
                    'std': np.nanstd(curve_data),
                    'n_wells': n_wells,
                    'samples_per_well': well_length
                }
                
                logger.info(f"   {curve_name}: {valid_samples}/{total_samples} valid ({completeness:.1%})")
                
                if completeness < 0.7:
                    logger.warning(f"   ⚠️  Low data completeness for {curve_name}: {completeness:.1%}")
            
            # Save validation report
            with open(self.results_dir / "data_validation_report.json", 'w') as f:
                json.dump(validation_report, f, indent=2, default=str)
            
            return data_dict
            
        except Exception as e:
            logger.error(f"❌ Failed to load data: {e}")
            raise
    
    def create_cross_validation_folds(self, data_dict: Dict, n_folds: int = 5) -> List[Tuple]:
        """Create cross-validation folds optimized for real HDF5 data"""
        logger.info(f"🔄 Creating {n_folds}-fold cross-validation splits...")
        
        # Get the total length of continuous sequences
        first_curve = next(iter(data_dict.values()))
        total_length = len(first_curve)
        
        logger.info(f"   📊 Total sequence length: {total_length}")
        
        # For real HDF5 data, use simple sequential splits to avoid overfitting
        # This matches the proven approach from initial implementation
        fold_size = total_length // n_folds
        folds = []
        
        for fold_idx in range(n_folds):
            # Create validation indices for this fold
            val_start = fold_idx * fold_size
            val_end = (fold_idx + 1) * fold_size if fold_idx < n_folds - 1 else total_length
            val_indices = np.arange(val_start, val_end)
            
            # Training indices are everything else
            train_indices = np.concatenate([
                np.arange(0, val_start),
                np.arange(val_end, total_length)
            ])
            
            # Create data splits
            train_data = {}
            val_data = {}
            
            for curve_name, curve_data in data_dict.items():
                train_data[curve_name] = curve_data[train_indices]
                val_data[curve_name] = curve_data[val_indices]
            
            folds.append((train_data, val_data, train_indices, val_indices))
            logger.info(f"   Fold {fold_idx + 1}: {len(train_indices)} samples train, "
                       f"{len(val_indices)} samples val")
        
        return folds
    
    def train_single_model(
        self, 
        target_curve: str,
        train_data: Dict,
        val_data: Dict,
        fold_idx: int
    ) -> Dict:
        """Train a single model for one target curve"""
        logger.info(f"🎯 Training model for {target_curve} (Fold {fold_idx + 1})")
        
        try:
            # Define input curves (all except target)
            all_curves = ['GR', 'CNL', 'DEN', 'AC', 'RLLD']
            input_curves = [curve for curve in all_curves if curve != target_curve and curve in train_data]
            
            if len(input_curves) < 2:
                logger.warning(f"   ⚠️  Insufficient input curves for {target_curve}: {input_curves}")
                return None
            
            logger.info(f"   📊 Input curves: {input_curves}")
            
            # Use curve-specific dataset for all curve types
            logger.info(f"   🎯 Using curve-specific dataset for {target_curve} prediction")
            train_dataset = CurveSpecificDataset(
                data_dict=train_data,
                input_curves=input_curves,
                target_curve=target_curve,
                normalizer=self.normalizer,
                sequence_length=640
            )
            
            val_dataset = CurveSpecificDataset(
                data_dict=val_data,
                input_curves=input_curves,
                target_curve=target_curve,
                normalizer=self.normalizer,
                sequence_length=640
            )
            
            if len(train_dataset) == 0 or len(val_dataset) == 0:
                logger.error(f"   ❌ Empty dataset for {target_curve}")
                return None
            
            logger.info(f"   📈 Training samples: {len(train_dataset)}")
            logger.info(f"   📊 Validation samples: {len(val_dataset)}")
            
            # Create model
            model = MWLT_Vp_Base(
                in_channels=len(input_curves),
                out_channels=1,
                feature_num=64
            )
            
            # Move model to device BEFORE training to prevent CUDA mismatch
            model = model.to(self.device)
            logger.info(f"   📱 Model moved to device: {self.device}")
            
            # Enhanced training configuration with VP-specific handling
            save_dir = self.results_dir / f"{target_curve}_fold_{fold_idx}"
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # Use simplified, curve-specific training approach for all curves
            logger.info(f"   🎯 Using curve-specific training configuration for {target_curve}")
            training_results = self._train_curve_specific_model(
                model, train_dataset, val_dataset, save_dir, target_curve, fold_idx
            )
            
            logger.info(f"   ✅ Training completed for {target_curve} (Fold {fold_idx + 1})")
            
            # Format R² and loss with proper handling for N/A values
            best_r2 = training_results.get('best_r2', 'N/A')
            best_loss = training_results.get('best_val_loss', 'N/A')
            
            r2_str = f"{best_r2:.4f}" if isinstance(best_r2, (int, float)) else str(best_r2)
            loss_str = f"{best_loss:.4f}" if isinstance(best_loss, (int, float)) else str(best_loss)
            
            logger.info(f"   � Best validation R²: {r2_str}")
            logger.info(f"   📉 Best validation loss: {loss_str}")
            
            return {
                'target_curve': target_curve,
                'fold': fold_idx,
                'input_curves': input_curves,
                'training_results': training_results,
                'model_path': save_dir / "best_model.pth",
                'config_path': save_dir / "training_config.json"
            }
            
        except Exception as e:
            logger.error(f"   ❌ Training failed for {target_curve} (Fold {fold_idx + 1}): {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _train_curve_specific_model(self, model, train_dataset, val_dataset, save_dir, target_curve, fold_idx):
        """Curve-specific training using optimized approach for each log curve type"""
        import torch.optim as optim
        from torch.utils.data import DataLoader
        from vp_predictor.utils import EarlyStopping, cal_R2, cal_RMSE
        from tqdm import tqdm
        import time
        
        logger.info(f"   🎯 Starting curve-specific training for {target_curve}")
        
        # Create data loaders with optimized batch size for small datasets
        batch_size = min(self.config.get('batch_size', 16), 8, len(train_dataset) // 4)
        batch_size = max(1, batch_size)  # Ensure at least 1
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, num_workers=0)
        
        # Setup training components with curve-specific optimizations
        learning_rate = self._get_curve_specific_lr(target_curve)
        weight_decay = self.config.get('weight_decay', 1e-5)
        optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        criterion = CurveSpecificLoss(target_curve)  # Use curve-specific loss
        
        # Early stopping
        patience = self.config.get('patience', 50)
        best_model_path = save_dir / 'best_model.pth'
        early_stopping = EarlyStopping(patience=patience, path=str(best_model_path))
        
        # Training history
        training_history = {'train_loss': [], 'val_loss': [], 'val_r2': [], 'val_rmse': []}
        
        epochs = self.config.get('max_epochs', 200)
        start_time = time.time()
        
        logger.info(f"   📊 Training samples: {len(train_dataset)}, Validation samples: {len(val_dataset)}")
        logger.info(f"   ⚙️  LR: {learning_rate}, Weight Decay: {weight_decay}, Loss: {target_curve}-specific")
        
        for epoch in range(1, epochs + 1):
            # Training phase
            model.train()
            total_train_loss = 0.0
            
            for inputs, targets in train_loader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                loss.backward()
                
                # Gradient clipping for stability
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                total_train_loss += loss.item()
            
            train_loss = total_train_loss / len(train_loader)
            
            # Validation phase
            model.eval()
            total_val_loss = 0.0
            predictions = []
            actuals = []
            
            with torch.no_grad():
                for inputs, targets in val_loader:
                    inputs, targets = inputs.to(self.device), targets.to(self.device)
                    outputs = model(inputs)
                    loss = criterion(outputs, targets)
                    
                    total_val_loss += loss.item()
                    predictions.extend(outputs.cpu().numpy().flatten())
                    actuals.extend(targets.cpu().numpy().flatten())
            
            val_loss = total_val_loss / len(val_loader)
            predictions = np.array(predictions)
            actuals = np.array(actuals)
            
            # Calculate metrics
            val_r2 = cal_R2(predictions, actuals)
            val_rmse = cal_RMSE(predictions, actuals)
            
            # Update history
            training_history['train_loss'].append(train_loss)
            training_history['val_loss'].append(val_loss)
            training_history['val_r2'].append(val_r2)
            training_history['val_rmse'].append(val_rmse)
            
            # Log progress
            if epoch % 10 == 0 or epoch == 1:
                logger.info(f"   Epoch {epoch:3d}/{epochs}: Train Loss: {train_loss:.6f}, "
                           f"Val Loss: {val_loss:.6f}, R²: {val_r2:.4f}, RMSE: {val_rmse:.2f}")
            
            # Early stopping check
            checkpoint_data = {
                "model_state_dict": model.state_dict(),
                "optimizer_state_dict": optimizer.state_dict(),
                "epoch": epoch,
                "train_loss": train_loss,
                "val_loss": val_loss,
                "loss": val_loss,  # For EarlyStopping compatibility
                "val_r2": val_r2,
                "val_rmse": val_rmse,
                "target_curve": target_curve
            }
            
            early_stopping(checkpoint_data, model)
            if early_stopping.early_stop:
                logger.info(f"   ⏰ Early stopping at epoch {epoch}")
                break
        
        training_time = time.time() - start_time
        logger.info(f"   ✅ {target_curve} training completed in {training_time:.2f} seconds")
        
        # Return results in expected format
        return {
            'best_r2': max(training_history['val_r2']) if training_history['val_r2'] else 0,
            'best_val_loss': min(training_history['val_loss']) if training_history['val_loss'] else float('inf'),
            'final_train_loss': training_history['train_loss'][-1] if training_history['train_loss'] else float('inf'),
            'training_history': training_history,
            'epochs_trained': epoch
        }
    
    def _get_curve_specific_lr(self, target_curve):
        """Get conservative learning rates for better convergence"""
        base_lr = self.config.get('learning_rate', 1e-4)
        
        # Conservative learning rates to ensure stable training
        lr_multipliers = {
            'AC': 1.0,      # VP prediction - keep proven rate
            'RLLD': 0.5,    # Resistivity - more conservative due to log scaling
            'CNL': 0.7,     # Neutron porosity - conservative start
            'GR': 0.7,      # Gamma ray - conservative start  
            'DEN': 0.7      # Density - conservative start
        }
        
        return base_lr * lr_multipliers.get(target_curve, 0.7)
    
    def calculate_confidence_metrics(self, fold_results: List[Dict], target_curve: str) -> Dict:
        """Calculate confidence intervals and cross-validation metrics"""
        logger.info(f"📊 Calculating confidence metrics for {target_curve}...")
        
        # Extract metrics from all folds
        r2_scores = []
        val_losses = []
        train_losses = []
        
        for result in fold_results:
            if result and result['training_results']:
                tr = result['training_results']
                if 'best_r2' in tr:
                    r2_scores.append(tr['best_r2'])
                if 'best_val_loss' in tr:
                    val_losses.append(tr['best_val_loss'])
                if 'final_train_loss' in tr:
                    train_losses.append(tr['final_train_loss'])
        
        if not r2_scores:
            logger.warning(f"   ⚠️  No valid R² scores for {target_curve}")
            return None
        
        # Calculate statistics
        confidence_metrics = {
            'r2_mean': np.mean(r2_scores),
            'r2_std': np.std(r2_scores),
            'r2_ci_lower': np.mean(r2_scores) - 1.96 * np.std(r2_scores) / np.sqrt(len(r2_scores)),
            'r2_ci_upper': np.mean(r2_scores) + 1.96 * np.std(r2_scores) / np.sqrt(len(r2_scores)),
            'val_loss_mean': np.mean(val_losses) if val_losses else None,
            'val_loss_std': np.std(val_losses) if val_losses else None,
            'n_folds': len(r2_scores),
            'successful_folds': len([r for r in fold_results if r is not None])
        }
        
        logger.info(f"   🎯 R² = {confidence_metrics['r2_mean']:.4f} ± {confidence_metrics['r2_std']:.4f}")
        logger.info(f"   📊 95% CI: [{confidence_metrics['r2_ci_lower']:.4f}, {confidence_metrics['r2_ci_upper']:.4f}]")
        
        return confidence_metrics
    
    def run_enhanced_training(self):
        """Run the complete enhanced training pipeline"""
        logger.info("🚀 Starting Enhanced Multi-Curve Training Pipeline")
        
        # Load and validate data
        data_dict = self.load_and_validate_data()
        
        # Create cross-validation folds
        cv_folds = self.create_cross_validation_folds(data_dict, n_folds=5)
        
        # Target curves to train
        target_curves = self.config.get('target_curves', ['AC', 'DEN', 'CNL', 'GR', 'RLLD'])
        available_curves = [curve for curve in target_curves if curve in data_dict]
        
        logger.info(f"🎯 Training targets: {available_curves}")
        
        # Results storage
        all_results = {}
        
        # Train each target curve with cross-validation
        for target_curve in available_curves:
            logger.info(f"\n{'='*80}")
            logger.info(f"🎯 TRAINING {target_curve} PREDICTION MODELS")
            logger.info(f"{'='*80}")
            
            fold_results = []
            
            # Train on each fold
            for fold_idx, (train_data, val_data, train_idx, val_idx) in enumerate(cv_folds):
                result = self.train_single_model(target_curve, train_data, val_data, fold_idx)
                fold_results.append(result)
            
            # Calculate confidence metrics
            confidence_metrics = self.calculate_confidence_metrics(fold_results, target_curve)
            
            all_results[target_curve] = {
                'fold_results': fold_results,
                'confidence_metrics': confidence_metrics
            }
        
        # Save comprehensive results
        self._save_comprehensive_results(all_results)
        
        # Generate summary report
        self._generate_summary_report(all_results)
        
        logger.info(f"\n🎉 Enhanced Training Pipeline Complete!")
        logger.info(f"📁 Results saved in: {self.results_dir}")
    
    def _save_comprehensive_results(self, all_results: Dict):
        """Save comprehensive training results"""
        logger.info("💾 Saving comprehensive results...")
        
        # Save detailed results
        results_file = self.results_dir / "enhanced_training_results.json"
        with open(results_file, 'w') as f:
            # Convert Path objects to strings for JSON serialization
            serializable_results = {}
            for curve, data in all_results.items():
                serializable_results[curve] = {
                    'confidence_metrics': data['confidence_metrics'],
                    'fold_results': []
                }
                
                for fold_result in data['fold_results']:
                    if fold_result:
                        serialized_fold = fold_result.copy()
                        if 'model_path' in serialized_fold:
                            serialized_fold['model_path'] = str(serialized_fold['model_path'])
                        if 'config_path' in serialized_fold:
                            serialized_fold['config_path'] = str(serialized_fold['config_path'])
                        serializable_results[curve]['fold_results'].append(serialized_fold)
            
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"   ✅ Results saved to: {results_file}")
    
    def _generate_summary_report(self, all_results: Dict):
        """Generate a comprehensive summary report"""
        logger.info("📋 Generating summary report...")
        
        report_file = self.results_dir / "training_summary_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Enhanced Multi-Curve Training Summary Report\n\n")
            f.write(f"**Generated**: {__import__('datetime').datetime.now()}\n")
            f.write(f"**Device**: {self.device}\n")
            f.write(f"**Cross-validation folds**: 5\n\n")
            
            f.write("## Model Performance Summary\n\n")
            f.write("| Target Curve | R² (Mean ± Std) | 95% Confidence Interval | Validation Loss | Success Rate |\n")
            f.write("|-------------|-----------------|------------------------|-----------------|-------------|\n")
            
            for curve, data in all_results.items():
                if data['confidence_metrics']:
                    cm = data['confidence_metrics']
                    val_loss_str = f"{cm['val_loss_mean']:.4f} ± {cm['val_loss_std']:.4f}" if cm['val_loss_mean'] is not None else "N/A"
                    f.write(f"| {curve} | {cm['r2_mean']:.4f} ± {cm['r2_std']:.4f} | "
                           f"[{cm['r2_ci_lower']:.4f}, {cm['r2_ci_upper']:.4f}] | "
                           f"{val_loss_str} | "
                           f"{cm['successful_folds']}/{cm['n_folds']} |\n")
                else:
                    f.write(f"| {curve} | Failed | Failed | Failed | 0/5 |\n")
            
            f.write("\n## Performance Analysis\n\n")
            
            # Performance classification
            for curve, data in all_results.items():
                if data['confidence_metrics']:
                    cm = data['confidence_metrics']
                    r2_mean = cm['r2_mean']
                    r2_std = cm['r2_std']
                    
                    if r2_mean > 0.8:
                        status = "🟢 Excellent"
                    elif r2_mean > 0.6:
                        status = "🟡 Good"
                    elif r2_mean > 0.3:
                        status = "🟠 Moderate"
                    else:
                        status = "🔴 Poor"
                    
                    f.write(f"### {curve} Prediction: {status}\n")
                    f.write(f"- **R² Score**: {r2_mean:.4f} ± {r2_std:.4f}\n")
                    f.write(f"- **Confidence Interval**: [{cm['r2_ci_lower']:.4f}, {cm['r2_ci_upper']:.4f}]\n")
                    f.write(f"- **Model Consistency**: {cm['successful_folds']}/{cm['n_folds']} successful folds\n\n")
                else:
                    f.write(f"### {curve} Prediction: ❌ Failed\n")
                    f.write(f"- **Status**: All training folds failed\n")
                    f.write(f"- **Recommendation**: Check data quality and model configuration\n\n")
            
            f.write("## Next Steps Recommendations\n\n")
            
            # Generate recommendations based on performance
            poor_performers = [curve for curve, data in all_results.items() 
                             if data['confidence_metrics'] and data['confidence_metrics']['r2_mean'] < 0.3]
            
            if poor_performers:
                f.write(f"### Priority Actions for Poor Performers: {', '.join(poor_performers)}\n")
                f.write("1. **Increase Training Data**: Current dataset may be insufficient\n")
                f.write("2. **Feature Engineering**: Consider additional input curves or transformations\n")
                f.write("3. **Architecture Optimization**: Experiment with different model architectures\n")
                f.write("4. **Hyperparameter Tuning**: Systematic optimization of training parameters\n\n")
            
            good_performers = [curve for curve, data in all_results.items() 
                             if data['confidence_metrics'] and data['confidence_metrics']['r2_mean'] > 0.6]
            
            if good_performers:
                f.write(f"### Production Ready Models: {', '.join(good_performers)}\n")
                f.write("1. **Deploy to Production**: Models show good performance and consistency\n")
                f.write("2. **Monitor Performance**: Implement real-time monitoring\n")
                f.write("3. **Collect Feedback**: Gather user feedback for continuous improvement\n\n")
        
        logger.info(f"   ✅ Summary report saved to: {report_file}")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="Enhanced Multi-Curve Training Pipeline")
    parser.add_argument('--config', type=str, help='Training configuration file (JSON)')
    parser.add_argument('--target-curves', nargs='+', 
                       choices=['AC', 'DEN', 'CNL', 'GR', 'RLLD'],
                       default=['AC', 'DEN', 'CNL', 'GR', 'RLLD'],
                       help='Target curves to train')
    parser.add_argument('--learning-rate', type=float, default=1e-4,
                       help='Learning rate for training')
    parser.add_argument('--max-epochs', type=int, default=200,
                       help='Maximum number of training epochs')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='Training batch size')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {
            'target_curves': args.target_curves,
            'learning_rate': args.learning_rate,
            'max_epochs': args.max_epochs,
            'batch_size': args.batch_size,
            'weight_decay': 1e-5,
            'patience': 50
        }
    
    # Run enhanced training pipeline
    pipeline = EnhancedTrainingPipeline(config)
    pipeline.run_enhanced_training()


if __name__ == "__main__":
    main()
