#!/usr/bin/env python3
"""
Model Validation and Performance Assessment Script
Addresses Priority 2: Cross-validation and confidence metrics

Features:
1. Cross-validation implementation
2. Confidence interval estimation
3. Model performance comparison
4. Overfitting detection
5. Prediction uncertainty quantification

Author: AI Assistant
Date: 2025-08-22
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from sklearn.model_selection import KFold
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from scipy import stats
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import argparse
import logging

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from vp_predictor import VpDataNormalizer
from vp_predictor.vp_model_improved import MWLT_Vp_Base
from vp_predictor.core.dataset import create_general_dataset
from vp_predictor.utils import get_device

# Try to import training examples, fallback to synthetic data if not available
try:
    from examples.production_training_examples import load_training_data
    TRAINING_EXAMPLES_AVAILABLE = True
except ImportError:
    TRAINING_EXAMPLES_AVAILABLE = False

    def load_real_training_data():
        """Load real training data from HDF5 files (copied from training script)"""
        from vp_predictor.las_processor import LASProcessor

        # Auto-detect data file locations
        # Prioritize project root directory for data files (A1.hdf5 and A2.hdf5 moved from examples/)
        script_dir = Path(__file__).parent
        project_root = script_dir.parent.parent  # Go up to Init_transformer/

        possible_paths = [
            project_root,  # Project root (primary location - where A1.hdf5 and A2.hdf5 are now located)
            script_dir,  # Current script directory
            script_dir.parent,  # multiwell/ directory
            project_root / "examples",  # examples/ directory (fallback)
            project_root / "archives" / "old_code" / "root_duplicates",  # Archived data files
        ]

        found_files = []
        for base_path in possible_paths:
            base_path = Path(base_path)
            a1_path = base_path / 'A1.hdf5'
            a2_path = base_path / 'A2.hdf5'

            if a1_path.exists() and a2_path.exists():
                found_files = [str(a1_path), str(a2_path)]
                logger.info(f"   ✅ Found data files in: {base_path.absolute()}")
                break

        if not found_files:
            logger.error("   ❌ Could not find A1.hdf5 and A2.hdf5 files!")
            return create_synthetic_validation_data()

        # Process real HDF5 data using proven approach
        processor = LASProcessor()
        all_data = {}
        sequence_length = 720
        augmentation_factor = 2.0
        step_size = int(sequence_length / augmentation_factor)

        for file_path in found_files:
            logger.info(f"   📂 Processing {file_path}...")
            curves = processor.process_hdf5_to_las_format(file_path)

            if 'AC' not in curves:
                logger.warning(f"   ⚠️  No AC curve found in {file_path}, skipping...")
                continue

            # Create overlapping windows
            data_length = len(curves['AC'])
            num_windows = max(1, (data_length - sequence_length) // step_size + 1)

            logger.info(f"   📊 Creating {num_windows} windows from {data_length} samples")

            for i in range(num_windows):
                start_idx = i * step_size
                end_idx = start_idx + sequence_length

                if end_idx > data_length:
                    start_idx = data_length - sequence_length
                    end_idx = data_length

                # Extract window and validate quality
                window_valid = True
                for curve_name in ['GR', 'CNL', 'DEN', 'AC', 'RLLD']:
                    if curve_name in curves:
                        data = curves[curve_name][start_idx:end_idx]
                        valid_ratio = np.sum(~np.isnan(data) & (data > 0)) / len(data)
                        if valid_ratio < 0.8:  # Quality threshold
                            window_valid = False
                            break

                if not window_valid:
                    continue

                # Store valid window data
                for curve_name in ['GR', 'CNL', 'DEN', 'AC', 'RLLD']:
                    if curve_name in curves:
                        if curve_name not in all_data:
                            all_data[curve_name] = []
                        all_data[curve_name].extend(curves[curve_name][start_idx:end_idx])

        # Convert to numpy arrays
        for curve_name in all_data:
            all_data[curve_name] = np.array(all_data[curve_name])

        total_length = len(all_data['AC']) if 'AC' in all_data else 0
        logger.info(f"   ✅ Loaded real data with {total_length} total samples")

        return all_data

    def create_synthetic_validation_data():
        """Create synthetic data for validation testing"""
        # Create longer sequences to ensure enough data for cross-validation
        # Each curve needs to be at least 720 * 5 = 3600 points for 5-fold CV
        total_length = 4000  # Extra buffer for cross-validation

        np.random.seed(123)  # Different seed from training

        return {
            'GR': np.random.randn(total_length) * 30 + 80,
            'CNL': np.random.randn(total_length) * 10 + 20,
            'DEN': np.random.randn(total_length) * 0.3 + 2.3,
            'AC': np.random.randn(total_length) * 30 + 200,
            'RLLD': np.random.exponential(10, total_length)
        }

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelValidator:
    """Comprehensive model validation with cross-validation and confidence metrics"""

    def __init__(self, config: Dict):
        self.config = config
        # Use SAME device as training to prevent performance degradation
        self.device = get_device()
        self.normalizer = VpDataNormalizer()

        # Set results directory within multiwell/validation/outputs/
        script_dir = Path(__file__).parent
        self.results_dir = script_dir / "outputs"
        self.results_dir.mkdir(exist_ok=True)

        # Setup plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        logger.info(f"🔬 Model Validator Initialized")
        logger.info(f"📱 Device: {self.device} (consistent with training)")


    def _summarize_model_info(self, model_info: Dict, target_curve: str) -> Dict:
        """Produce a compact, robust model_info summary for reporting."""
        summary = {'target_curve': target_curve}
        try:
            if isinstance(model_info, dict):
                # Single model path case
                if 'model_path' in model_info:
                    summary['model_path'] = str(model_info['model_path'])
                    if 'config' in model_info:
                        summary['config'] = model_info['config']
                else:
                    # Fold models dict
                    fold_entries = []
                    for k, v in model_info.items():
                        if isinstance(k, int) and isinstance(v, dict):
                            entry = {'fold': k}
                            if 'model_path' in v:
                                entry['model_path'] = str(v['model_path'])
                            if 'config' in v:
                                entry['config'] = v['config']
                            fold_entries.append(entry)
                    if fold_entries:
                        summary['fold_models'] = sorted(fold_entries, key=lambda e: e['fold'])
        except Exception:
            pass
        return summary

        logger.info(f"🔬 Model Validator Initialized")
        logger.info(f"📱 Device: {self.device} (consistent with training)")

    def load_trained_models(self, models_dir: Path) -> Dict[str, Dict]:
        """Load all available trained models, organized by target curve and fold"""
        logger.info("📂 Loading trained models...")

        models = defaultdict(dict)  # Changed to support per-fold models

        # Check common locations for trained models (prioritize multiwell structure)
        script_dir = Path(__file__).parent
        multiwell_dir = script_dir.parent

        possible_dirs = [
            models_dir,
            multiwell_dir / "training" / "outputs",  # New training outputs location
            script_dir.parent.parent / "archives" / "old_code" / "output_directories" / "enhanced_training_outputs",  # Archived outputs
            multiwell_dir / "production_models",
            multiwell_dir / "production_training_outputs",
        ]

        for check_dir in possible_dirs:
            if not check_dir.exists():
                continue

            logger.info(f"Checking for models in: {check_dir}")

            for model_dir in check_dir.iterdir():
                if not model_dir.is_dir():
                    continue

                model_file = model_dir / "best_model.pth"
                config_file = model_dir / "training_config.json"

                if not (model_file.exists() and config_file.exists()):
                    continue

                try:
                    # Load config
                    with open(config_file, 'r') as f:
                        model_config = json.load(f)

                    # Extract target curve and fold from directory name
                    dir_name = model_dir.name.lower()

                    if '_fold_' in dir_name:
                        # Handle fold-specific naming (e.g., AC_fold_0, DEN_fold_2)
                        curve_part, _, fold_part = dir_name.rpartition('_fold_')
                        target_curve = curve_part.upper()
                        try:
                            fold_idx = int(fold_part)
                        except ValueError:
                            logger.warning(f"   ⚠️  Invalid fold number in {model_dir.name}")
                            continue
                    else:
                        # Handle non-fold naming - treat as fold 0
                        target_curve = model_config.get('target_curve', model_dir.name.replace('_model', '').upper())
                        fold_idx = 0

                    # Validate target curve
                    if target_curve not in ['AC', 'DEN', 'CNL', 'GR', 'RLLD']:
                        logger.warning(f"   ⚠️  Unknown target curve: {target_curve}")
                        continue

                    models[target_curve][fold_idx] = {
                        'model_path': model_file,
                        'config_path': config_file,
                        'config': model_config,
                        'directory': model_dir
                    }

                    logger.info(f"   ✅ Found {target_curve} fold {fold_idx} model")

                except Exception as e:
                    logger.warning(f"   ⚠️  Failed to load model from {model_dir}: {e}")

        # Convert defaultdict back to regular dict for return
        return dict(models)

    def create_model_instance(self, target_curve: str, input_curves: List[str]) -> nn.Module:
        """Create a model instance with appropriate architecture"""
        model = MWLT_Vp_Base(
            in_channels=len(input_curves),
            out_channels=1,
            feature_num=64
        )
        return model

    def load_model_weights(self, model: nn.Module, model_path: Path) -> bool:
        """Load trained weights into model"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)

            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)

            # Ensure model is on the correct device
            model.to(self.device)
            model.eval()
            return True

        except Exception as e:
            logger.error(f"❌ Failed to load model weights: {e}")
            return False

    def perform_cross_validation(
        self,
        target_curve: str,
        data_dict: Dict[str, np.ndarray],
        model_info: Optional[Dict] = None,
        n_folds: int = 5
    ) -> Dict:
        """Perform comprehensive cross-validation"""
        logger.info(f"🔄 Cross-validating {target_curve} prediction ({n_folds} folds)")

        # Define input curves
        all_curves = ['GR', 'CNL', 'DEN', 'AC', 'RLLD']
        input_curves = [curve for curve in all_curves if curve != target_curve and curve in data_dict]

        if len(input_curves) < 2:
            logger.error(f"❌ Insufficient input curves for {target_curve}: {input_curves}")
            return None

        logger.info(f"   📊 Input curves: {input_curves}")

        # Prepare sequential folds (matching training)
        first_curve = data_dict[target_curve]
        n_samples = len(first_curve)

        # Create sequential folds like training (blocked splits)
        fold_size = n_samples // n_folds
        folds = []
        for fold_idx in range(n_folds):
            val_start = fold_idx * fold_size
            val_end = (fold_idx + 1) * fold_size if fold_idx < n_folds - 1 else n_samples
            val_idx = np.arange(val_start, val_end)
            train_idx = np.concatenate([np.arange(0, val_start), np.arange(val_end, n_samples)])
            folds.append((train_idx, val_idx))

        # Results storage
        fold_results = []
        predictions_all = []
        actuals_all = []

        for fold_idx, (train_idx, val_idx) in enumerate(folds):
            logger.info(f"   📋 Fold {fold_idx + 1}/{n_folds}")

            # Create fold data
            train_data = {curve: data[train_idx] for curve, data in data_dict.items()}
            val_data = {curve: data[val_idx] for curve, data in data_dict.items()}

            # Use SAME dataset class as training to prevent data leakage
            try:
                from multiwell.training.enhanced_multi_curve_training import CurveSpecificDataset
            except ImportError as e:
                raise RuntimeError("CurveSpecificDataset must be available for consistent validation") from e

            val_dataset = CurveSpecificDataset(
                data_dict=val_data,
                input_curves=input_curves,
                target_curve=target_curve,
                normalizer=self.normalizer,
                sequence_length=640  # Same as training
            )

            if len(val_dataset) == 0:
                logger.warning(f"      ⚠️  Empty validation set for fold {fold_idx + 1}")
                continue

            # Create and load fold-specific model
            model = self.create_model_instance(target_curve, input_curves)
            model = model.to(self.device)

            # Try to load fold-specific model first, then fallback to general model
            model_loaded = False

            # Case 1: model_info is a mapping of fold_idx -> fold_info (current expected structure)
            if isinstance(model_info, dict):
                # Detect int keys (fold indices)
                fold_keys = [k for k in model_info.keys() if isinstance(k, int)]
                if fold_keys:
                    # Require exact fold model to avoid evaluating with untrained weights
                    if fold_idx not in model_info:
                        logger.warning(f"      ⚠️  No trained model found for fold {fold_idx}; skipping this fold")
                        continue
                    fold_info = model_info.get(fold_idx)
                    if not (isinstance(fold_info, dict) and 'model_path' in fold_info):
                        logger.warning(f"      ⚠️  Invalid fold info for fold {fold_idx}; skipping this fold")
                        continue
                    if self.load_model_weights(model, fold_info['model_path']):
                        logger.info(f"      ✅ Loaded model for fold {fold_idx} ({target_curve}_fold_{fold_idx})")
                        model_loaded = True
                    else:
                        logger.warning(f"      ⚠️  Failed to load weights for fold {fold_idx}; skipping this fold")
                        continue
                # Case 2: legacy single-model dict with 'model_path'
                elif 'model_path' in model_info:
                    if self.load_model_weights(model, model_info['model_path']):
                        logger.info("      ✅ Loaded legacy single model")
                        model_loaded = True
                else:
                    logger.warning("      ⚠️  No fold models or legacy model_path provided; skipping this fold")
                    continue

            if not model_loaded:
                # Should not reach here due to continues above, but keep as guard
                logger.warning(f"      ⚠️  Using untrained model (for baseline)")
                continue

            # Make predictions
            predictions, actuals = self._predict_on_dataset(model, val_dataset)

            if predictions is None:
                continue

            # Calculate metrics in normalized units
            r2 = r2_score(actuals, predictions)
            mae = mean_absolute_error(actuals, predictions)
            rmse = np.sqrt(mean_squared_error(actuals, predictions))
            correlation = np.corrcoef(actuals, predictions)[0, 1]

            # Calculate physical unit metrics for AC curve
            r2_phys = None
            mae_phys = None
            rmse_phys = None
            if target_curve == 'AC':
                try:
                    # Denormalize to physical units (μs/ft)
                    preds_phys = self.normalizer.denormalize_vp(torch.tensor(predictions)).numpy()
                    acts_phys = self.normalizer.denormalize_vp(torch.tensor(actuals)).numpy()

                    r2_phys = r2_score(acts_phys, preds_phys)
                    mae_phys = mean_absolute_error(acts_phys, preds_phys)
                    rmse_phys = np.sqrt(mean_squared_error(acts_phys, preds_phys))

                    logger.info(f"      📈 Physical units - R²: {r2_phys:.4f}, MAE: {mae_phys:.2f} μs/ft, RMSE: {rmse_phys:.2f} μs/ft")
                except Exception as e:
                    logger.warning(f"      ⚠️  Failed to calculate physical unit metrics: {e}")

            fold_result = {
                'fold': fold_idx + 1,
                'r2': r2,
                'r2_phys': r2_phys,
                'mae': mae,
                'mae_phys': mae_phys,
                'rmse': rmse,
                'rmse_phys': rmse_phys,
                'correlation': correlation,
                'n_samples': len(predictions),
                'val_indices': val_idx.tolist(),
                'model_used': f"fold_{fold_idx}" if model_loaded else "untrained"
            }

            fold_results.append(fold_result)
            predictions_all.extend(predictions)
            actuals_all.extend(actuals)

            logger.info(f"      📈 R²: {r2:.4f}, MAE: {mae:.4f}, RMSE: {rmse:.4f}")

        # Calculate overall statistics
        if not fold_results:
            return None

        # Extract metrics arrays
        r2_scores = [r['r2'] for r in fold_results]
        mae_scores = [r['mae'] for r in fold_results]
        rmse_scores = [r['rmse'] for r in fold_results]
        corr_scores = [r['correlation'] for r in fold_results]

        # Extract physical unit metrics if available
        r2_phys_scores = [r['r2_phys'] for r in fold_results if r['r2_phys'] is not None]
        mae_phys_scores = [r['mae_phys'] for r in fold_results if r['mae_phys'] is not None]
        rmse_phys_scores = [r['rmse_phys'] for r in fold_results if r['rmse_phys'] is not None]

        # Calculate confidence intervals
        confidence_level = 0.95
        alpha = 1 - confidence_level

        def calculate_ci(values):
            if not values:
                return None, None, (None, None)
            mean_val = np.mean(values)
            sem = stats.sem(values)  # Standard error of mean
            ci = stats.t.interval(confidence_level, len(values)-1, loc=mean_val, scale=sem)
            return mean_val, sem, ci

        r2_mean, r2_sem, r2_ci = calculate_ci(r2_scores)
        mae_mean, mae_sem, mae_ci = calculate_ci(mae_scores)
        rmse_mean, rmse_sem, rmse_ci = calculate_ci(rmse_scores)

        # Calculate physical unit statistics
        r2_phys_mean, r2_phys_sem, r2_phys_ci = calculate_ci(r2_phys_scores)
        mae_phys_mean, mae_phys_sem, mae_phys_ci = calculate_ci(mae_phys_scores)
        rmse_phys_mean, rmse_phys_sem, rmse_phys_ci = calculate_ci(rmse_phys_scores)

        # Build summary statistics
        summary_stats = {
            'r2': {
                'mean': r2_mean,
                'std': np.std(r2_scores),
                'sem': r2_sem,
                'ci_lower': r2_ci[0],
                'ci_upper': r2_ci[1],
                'min': min(r2_scores),
                'max': max(r2_scores)
            },
            'mae': {
                'mean': mae_mean,
                'std': np.std(mae_scores),
                'sem': mae_sem,
                'ci_lower': mae_ci[0],
                'ci_upper': mae_ci[1]
            },
            'rmse': {
                'mean': rmse_mean,
                'std': np.std(rmse_scores),
                'sem': rmse_sem,
                'ci_lower': rmse_ci[0],
                'ci_upper': rmse_ci[1]
            }
        }

        # Add physical unit statistics if available
        if r2_phys_scores:
            summary_stats['r2_phys'] = {
                'mean': r2_phys_mean,
                'std': np.std(r2_phys_scores),
                'sem': r2_phys_sem,
                'ci_lower': r2_phys_ci[0],
                'ci_upper': r2_phys_ci[1],
                'min': min(r2_phys_scores),
                'max': max(r2_phys_scores)
            }

        if mae_phys_scores:
            summary_stats['mae_phys'] = {
                'mean': mae_phys_mean,
                'std': np.std(mae_phys_scores),
                'sem': mae_phys_sem,
                'ci_lower': mae_phys_ci[0],
                'ci_upper': mae_phys_ci[1]
            }

        if rmse_phys_scores:
            summary_stats['rmse_phys'] = {
                'mean': rmse_phys_mean,
                'std': np.std(rmse_phys_scores),
                'sem': rmse_phys_sem,
                'ci_lower': rmse_phys_ci[0],
                'ci_upper': rmse_phys_ci[1]
            }

        cross_validation_results = {
            'target_curve': target_curve,
            'input_curves': input_curves,
            'n_folds': len(fold_results),
            'fold_results': fold_results,
            'summary_statistics': summary_stats,
            'all_predictions': predictions_all,
            'all_actuals': actuals_all
        }

        logger.info(f"   🎯 Overall Performance:")
        logger.info(f"      R² (normalized) = {r2_mean:.4f} ± {r2_sem:.4f} (95% CI: [{r2_ci[0]:.4f}, {r2_ci[1]:.4f}])")
        logger.info(f"      MAE (normalized) = {mae_mean:.4f} ± {mae_sem:.4f}")
        logger.info(f"      RMSE (normalized) = {rmse_mean:.4f} ± {rmse_sem:.4f}")

        # Log physical unit metrics if available
        if r2_phys_scores:
            logger.info(f"      R² (physical) = {r2_phys_mean:.4f} ± {r2_phys_sem:.4f} (95% CI: [{r2_phys_ci[0]:.4f}, {r2_phys_ci[1]:.4f}])")
            logger.info(f"      MAE (physical) = {mae_phys_mean:.2f} ± {mae_phys_sem:.2f} μs/ft")
            logger.info(f"      RMSE (physical) = {rmse_phys_mean:.2f} ± {rmse_phys_sem:.2f} μs/ft")

        return cross_validation_results

    def _predict_on_dataset(self, model: nn.Module, dataset) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """Make predictions on a dataset"""
        model.eval()
        device = next(model.parameters()).device  # Get model's device

        predictions = []
        actuals = []

        with torch.no_grad():
            for i in range(len(dataset)):
                try:
                    inputs, targets = dataset[i]
                    inputs = inputs.unsqueeze(0).to(device)  # Add batch dimension and move to device
                    targets = targets.unsqueeze(0).to(device)  # Move to device

                    # Make prediction
                    pred = model(inputs)

                    # Convert to numpy and flatten
                    pred_np = pred.cpu().numpy().flatten()
                    target_np = targets.cpu().numpy().flatten()

                    predictions.extend(pred_np)
                    actuals.extend(target_np)

                except Exception as e:
                    logger.warning(f"      ⚠️  Prediction failed for sample {i}: {e}")
                    continue

        if not predictions:
            logger.error("      ❌ No successful predictions")
            return None, None

        return np.array(predictions), np.array(actuals)

    def detect_overfitting(self, model_info: Dict, target_curve: str) -> Dict:
        """Detect overfitting by analyzing training history
        Accepts either a fold-level dict with 'directory' or a dict of folds {fold_idx: {...}}.
        """
        logger.info(f"🔍 Analyzing overfitting for {target_curve}")

        # Resolve a directory containing training_history.json
        history_dir = None
        if isinstance(model_info, dict):
            if 'directory' in model_info:
                history_dir = model_info['directory']
            else:
                # Assume dict of folds; prefer fold 0; else exact one with a directory
                fold_keys = [k for k in model_info.keys() if isinstance(k, int)]
                for key in ([0] if 0 in model_info else sorted(fold_keys)):
                    info = model_info.get(key, {})
                    if isinstance(info, dict) and 'directory' in info:
                        history_dir = info['directory']
                        break

        if history_dir is None:
            logger.warning("   ⚠️  No training directory available for overfitting analysis")
            return None

        history_file = history_dir / "training_history.json"
        if not history_file.exists():
            logger.warning(f"   ⚠️  No training history found at {history_file}")
            return None

        try:
            with open(history_file, 'r') as f:
                history = json.load(f)

            train_losses = history.get('train_loss', [])
            val_losses = history.get('val_loss', [])

            if not train_losses or not val_losses:
                logger.warning(f"   ⚠️  Incomplete training history")
                return None

            # Calculate overfitting indicators
            min_epochs = min(len(train_losses), len(val_losses))
            train_losses = train_losses[:min_epochs]
            val_losses = val_losses[:min_epochs]

            # Find best epoch (minimum validation loss)
            best_epoch = int(np.argmin(val_losses))
            best_val_loss = float(val_losses[best_epoch])
            best_train_loss = float(train_losses[best_epoch])

            # Calculate final gaps
            final_train_loss = float(train_losses[-1])
            final_val_loss = float(val_losses[-1])
            final_gap = float(final_val_loss - final_train_loss)

            # Overfitting score: higher means more overfitting
            overfitting_score = max(0.0, final_gap / final_val_loss) * 100.0 if final_val_loss != 0 else 0.0

            # Trend analysis (last 20% of training)
            trend_start = max(0, int(0.8 * len(val_losses)))
            val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])

            overfitting_analysis = {
                'best_epoch': best_epoch,
                'total_epochs': len(val_losses),
                'best_train_loss': best_train_loss,
                'best_val_loss': best_val_loss,
                'final_train_loss': final_train_loss,
                'final_val_loss': final_val_loss,
                'train_val_gap': final_gap,
                'overfitting_score': overfitting_score,
                'validation_trend': val_trend,  # Positive = getting worse
                'early_stopping_triggered': best_epoch < len(val_losses) * 0.8,
                'overfitting_detected': (overfitting_score > 10.0) or (val_trend > 0.001)
            }

            # Interpretation
            if overfitting_analysis['overfitting_detected']:
                logger.warning(f"   🟠 Overfitting detected (score: {overfitting_score:.1f}%)")
            else:
                logger.info(f"   🟢 No significant overfitting (score: {overfitting_score:.1f}%)")

            return overfitting_analysis

        except Exception as e:
            logger.error(f"   ❌ Failed to analyze overfitting: {e}")
            return None

    def estimate_prediction_uncertainty(
        self,
        model_info: Dict,
        target_curve: str,
        test_data: Dict,
        n_monte_carlo: int = 100
    ) -> Dict:
        """Estimate prediction uncertainty using Monte Carlo dropout"""
        logger.info(f"🎲 Estimating prediction uncertainty for {target_curve}")

        # Define input curves
        all_curves = ['GR', 'CNL', 'DEN', 'AC', 'RLLD']
        input_curves = [curve for curve in all_curves if curve != target_curve and curve in test_data]

        # Use SAME dataset class as training for uncertainty analysis
        try:
            from multiwell.training.enhanced_multi_curve_training import CurveSpecificDataset
        except ImportError as e:
            raise RuntimeError("CurveSpecificDataset must be available for consistent uncertainty analysis") from e

        test_dataset = CurveSpecificDataset(
            data_dict=test_data,
            input_curves=input_curves,
            target_curve=target_curve,
            normalizer=self.normalizer,
            sequence_length=640  # Same as training
        )

        if len(test_dataset) == 0:
            logger.error(f"   ❌ Empty test dataset")
            return None

        # Try fold ensemble uncertainty first (preferred method)
        fold_predictions = []
        if isinstance(model_info, dict):
            # Collect fold models from int keys
            fold_models = {k: v for k, v in model_info.items() if isinstance(k, int) and isinstance(v, dict) and 'model_path' in v}
            if fold_models:
                logger.info(f"   🎯 Using fold ensemble uncertainty estimation")
                for fold_idx, fold_model_info in sorted(fold_models.items()):
                    model = self.create_model_instance(target_curve, input_curves)
                    model = model.to(self.device)

                    if self.load_model_weights(model, fold_model_info['model_path']):
                        logger.info(f"   📊 Loading fold {fold_idx} for ensemble")
                        predictions, actuals = self._predict_on_dataset(model, test_dataset)
                        if predictions is not None:
                            fold_predictions.append(predictions)

                if len(fold_predictions) >= 2:
                    # Use fold ensemble variance as epistemic uncertainty
                    fold_predictions = np.stack(fold_predictions, axis=0)  # Shape: (n_folds, n_samples)
                    mean_predictions = np.mean(fold_predictions, axis=0)
                    std_predictions = np.std(fold_predictions, axis=0)

                    # Calculate prediction intervals from fold variance
                    lower_bound = np.percentile(fold_predictions, 2.5, axis=0)
                    upper_bound = np.percentile(fold_predictions, 97.5, axis=0)

                    uncertainty_stats = []
                    for i in range(len(mean_predictions)):
                        uncertainty_stats.append({
                            'sample_idx': i,
                            'mean_prediction': mean_predictions[i],
                            'std_prediction': std_predictions[i],
                            'lower_95_ci': lower_bound[i],
                            'upper_95_ci': upper_bound[i],
                            'actual': actuals[i] if actuals is not None else None,
                            'uncertainty_ratio': std_predictions[i] / (abs(mean_predictions[i]) + 1e-8)
                        })

                    # Overall uncertainty summary
                    avg_uncertainty_ratio = np.mean([stat['uncertainty_ratio'] for stat in uncertainty_stats])

                    uncertainty_results = {
                        'target_curve': target_curve,
                        'method': 'fold_ensemble',
                        'n_folds_used': len(fold_predictions),
                        'n_samples_analyzed': len(uncertainty_stats),
                        'sample_uncertainties': uncertainty_stats[:10],  # Limit for JSON size
                        'average_uncertainty_ratio': avg_uncertainty_ratio,
                        'high_uncertainty_threshold': 0.1,
                        'high_uncertainty_samples': len([s for s in uncertainty_stats if s['uncertainty_ratio'] > 0.1])
                    }

                    logger.info(f"   🎯 Fold ensemble uncertainty ratio: {avg_uncertainty_ratio:.4f}")
                    logger.info(f"   ⚠️  High uncertainty samples: {uncertainty_results['high_uncertainty_samples']}/{len(uncertainty_stats)}")

                    return uncertainty_results

        # Fallback to Monte Carlo dropout if fold ensemble not available
        logger.info(f"   🎲 Falling back to Monte Carlo dropout uncertainty")

        # Load single model
        model = self.create_model_instance(target_curve, input_curves)
        model = model.to(self.device)

        if hasattr(model_info, 'get') and 'model_path' in model_info:
            model_path = model_info['model_path']
        elif isinstance(model_info, dict) and target_curve in model_info:
            # Use first available fold model
            fold_models = model_info[target_curve]
            if isinstance(fold_models, dict):
                model_path = next(iter(fold_models.values()))['model_path']
            else:
                logger.error(f"   ❌ Invalid model_info structure")
                return None
        else:
            logger.error(f"   ❌ No model available for uncertainty estimation")
            return None

        if not self.load_model_weights(model, model_path):
            logger.error(f"   ❌ Failed to load model weights")
            return None

        # Enable dropout for uncertainty estimation
        def enable_dropout(m):
            if type(m) == nn.Dropout:
                m.train()

        # Collect predictions with Monte Carlo dropout
        model.train()  # Enable dropout
        model.apply(enable_dropout)

        all_predictions = []
        actuals = []

        with torch.no_grad():
            for sample_idx in range(min(len(test_dataset), 10)):  # Limit for efficiency
                inputs, targets = test_dataset[sample_idx]
                inputs = inputs.unsqueeze(0).to(self.device)

                # Multiple forward passes with dropout
                sample_predictions = []
                for _ in range(n_monte_carlo):
                    pred = model(inputs)
                    sample_predictions.append(pred.cpu().numpy().flatten())

                all_predictions.append(np.array(sample_predictions))
                actuals.append(targets.cpu().numpy().flatten())

        # Calculate uncertainty statistics
        uncertainty_stats = []
        for i, predictions in enumerate(all_predictions):
            mean_pred = np.mean(predictions, axis=0)
            std_pred = np.std(predictions, axis=0)

            # Calculate prediction intervals
            lower_bound = np.percentile(predictions, 2.5, axis=0)
            upper_bound = np.percentile(predictions, 97.5, axis=0)

            uncertainty_stats.append({
                'sample_idx': i,
                'mean_prediction': mean_pred,
                'std_prediction': std_pred,
                'lower_95_ci': lower_bound,
                'upper_95_ci': upper_bound,
                'actual': actuals[i],
                'uncertainty_ratio': np.mean(std_pred / (np.abs(mean_pred) + 1e-8))
            })

        # Overall uncertainty summary
        avg_uncertainty_ratio = np.mean([stat['uncertainty_ratio'] for stat in uncertainty_stats])

        uncertainty_results = {
            'target_curve': target_curve,
            'method': 'monte_carlo_dropout',
            'n_monte_carlo': n_monte_carlo,
            'n_samples_analyzed': len(uncertainty_stats),
            'sample_uncertainties': uncertainty_stats,
            'average_uncertainty_ratio': avg_uncertainty_ratio,
            'high_uncertainty_threshold': 0.1,
            'high_uncertainty_samples': len([s for s in uncertainty_stats if s['uncertainty_ratio'] > 0.1])
        }

        logger.info(f"   🎯 Average uncertainty ratio: {avg_uncertainty_ratio:.4f}")
        logger.info(f"   ⚠️  High uncertainty samples: {uncertainty_results['high_uncertainty_samples']}/{len(uncertainty_stats)}")

        return uncertainty_results

    def create_validation_visualizations(self, validation_results: Dict):
        """Create comprehensive validation visualizations"""
        logger.info("📊 Creating validation visualizations...")

        target_curve = validation_results['target_curve']

        # Create subplots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{target_curve} Model Validation Results', fontsize=16, fontweight='bold')

        # 1. Cross-validation scores
        ax1 = axes[0, 0]
        fold_results = validation_results['fold_results']
        folds = [r['fold'] for r in fold_results]
        r2_scores = [r['r2'] for r in fold_results]

        ax1.bar(folds, r2_scores, alpha=0.7, color='skyblue')
        ax1.axhline(y=validation_results['summary_statistics']['r2']['mean'],
                   color='red', linestyle='--', label='Mean R²')
        ax1.set_xlabel('Fold')
        ax1.set_ylabel('R² Score')
        ax1.set_title('Cross-Validation R² Scores')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. Predictions vs Actuals
        ax2 = axes[0, 1]
        predictions = validation_results['all_predictions']
        actuals = validation_results['all_actuals']

        ax2.scatter(actuals, predictions, alpha=0.6, s=20)

        # Perfect prediction line
        min_val = min(min(actuals), min(predictions))
        max_val = max(max(actuals), max(predictions))
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', label='Perfect Prediction')

        ax2.set_xlabel(f'Actual {target_curve}')
        ax2.set_ylabel(f'Predicted {target_curve}')
        ax2.set_title('Predictions vs Actuals')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Add R² text
        overall_r2 = r2_score(actuals, predictions)
        ax2.text(0.05, 0.95, f'R² = {overall_r2:.4f}',
                transform=ax2.transAxes, fontsize=12,
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 3. Residuals plot
        ax3 = axes[0, 2]
        residuals = np.array(predictions) - np.array(actuals)
        ax3.scatter(actuals, residuals, alpha=0.6, s=20)
        ax3.axhline(y=0, color='red', linestyle='--')
        ax3.set_xlabel(f'Actual {target_curve}')
        ax3.set_ylabel('Residuals')
        ax3.set_title('Residuals Plot')
        ax3.grid(True, alpha=0.3)

        # 4. Performance metrics distribution
        ax4 = axes[1, 0]
        metrics = ['R²', 'MAE', 'RMSE']
        means = [
            validation_results['summary_statistics']['r2']['mean'],
            validation_results['summary_statistics']['mae']['mean'],
            validation_results['summary_statistics']['rmse']['mean']
        ]
        errors = [
            validation_results['summary_statistics']['r2']['sem'],
            validation_results['summary_statistics']['mae']['sem'],
            validation_results['summary_statistics']['rmse']['sem']
        ]

        bars = ax4.bar(metrics, means, yerr=errors, capsize=5, alpha=0.7)
        ax4.set_ylabel('Metric Value')
        ax4.set_title('Performance Metrics (Mean ± SEM)')
        ax4.grid(True, alpha=0.3)

        # Color code bars based on performance
        if means[0] > 0.8:  # R²
            bars[0].set_color('green')
        elif means[0] > 0.6:
            bars[0].set_color('orange')
        else:
            bars[0].set_color('red')

        # 5. Confidence intervals visualization
        ax5 = axes[1, 1]
        r2_stats = validation_results['summary_statistics']['r2']

        ax5.errorbar([1], [r2_stats['mean']],
                    yerr=[[r2_stats['mean'] - r2_stats['ci_lower']],
                          [r2_stats['ci_upper'] - r2_stats['mean']]],
                    fmt='o', capsize=10, capthick=2, markersize=10)

        ax5.set_xlim(0.5, 1.5)
        ax5.set_ylabel('R² Score')
        ax5.set_title('R² Score with 95% Confidence Interval')
        ax5.set_xticks([1])
        ax5.set_xticklabels([target_curve])
        ax5.grid(True, alpha=0.3)

        # Add confidence interval text
        ax5.text(1, r2_stats['mean'] + 0.1,
                f"95% CI: [{r2_stats['ci_lower']:.3f}, {r2_stats['ci_upper']:.3f}]",
                ha='center', fontsize=10,
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        # 6. Model quality assessment
        ax6 = axes[1, 2]

        # Quality categories
        r2_mean = r2_stats['mean']
        if r2_mean > 0.8:
            quality = "Excellent"
            color = 'green'
        elif r2_mean > 0.6:
            quality = "Good"
            color = 'orange'
        elif r2_mean > 0.3:
            quality = "Moderate"
            color = 'yellow'
        else:
            quality = "Poor"
            color = 'red'

        ax6.text(0.5, 0.6, f"Model Quality:\n{quality}",
                ha='center', va='center', fontsize=16, fontweight='bold',
                transform=ax6.transAxes,
                bbox=dict(boxstyle='round', facecolor=color, alpha=0.8))

        ax6.text(0.5, 0.3, f"R² = {r2_mean:.4f}\nMAE = {means[1]:.4f}",
                ha='center', va='center', fontsize=12,
                transform=ax6.transAxes)

        ax6.set_xlim(0, 1)
        ax6.set_ylim(0, 1)
        ax6.axis('off')

        plt.tight_layout()

        # Save plot
        plot_path = self.results_dir / f"{target_curve}_validation_results.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        logger.info(f"   ✅ Validation plot saved: {plot_path}")

        plt.show()

    def run_comprehensive_validation(self):
        """Run comprehensive model validation pipeline"""
        logger.info("🚀 Starting Comprehensive Model Validation")

        # Load data using SAME method as training to prevent inconsistencies
        logger.info("📂 Loading data...")

        try:
            if TRAINING_EXAMPLES_AVAILABLE:
                data_dict = load_training_data()
                logger.info("   ✅ Loaded real training data")
            else:
                logger.info("   📂 Loading same HDF5 data as training...")
                data_dict = load_real_training_data()
                logger.info("   ✅ Loaded same HDF5 data as training")
        except Exception as e:
            logger.warning(f"   ⚠️  Failed to load training data: {e}")
            logger.info("   📊 Falling back to synthetic validation data")
            data_dict = create_synthetic_validation_data()
            logger.info("   ✅ Generated synthetic validation data")

        # Load trained models from multiwell training outputs
        script_dir = Path(__file__).parent
        models_dir = script_dir.parent / "training" / "outputs"
        trained_models = self.load_trained_models(models_dir)

        if not trained_models:
            logger.error("❌ No trained models found!")
            return

        # Validation results storage
        all_validation_results = {}

        # Validate each model
        for target_curve, model_info in trained_models.items():
            logger.info(f"\n{'='*80}")
            logger.info(f"🎯 VALIDATING {target_curve} PREDICTION MODEL")
            logger.info(f"{'='*80}")

            try:
                # Cross-validation
                cv_results = self.perform_cross_validation(
                    target_curve, data_dict, model_info, n_folds=5
                )

                if cv_results is None:
                    logger.error(f"❌ Cross-validation failed for {target_curve}")
                    continue

                # Overfitting analysis
                overfitting_results = self.detect_overfitting(model_info, target_curve)

                # Uncertainty estimation
                uncertainty_results = self.estimate_prediction_uncertainty(
                    model_info, target_curve, data_dict, n_monte_carlo=50
                )

                # Create visualizations
                self.create_validation_visualizations(cv_results)

                # Compile results
                all_validation_results[target_curve] = {
                    'cross_validation': cv_results,
                    'overfitting_analysis': overfitting_results,
                    'uncertainty_analysis': uncertainty_results,
                    'model_info': self._summarize_model_info(model_info, target_curve)

                }

                logger.info(f"✅ Validation completed for {target_curve}")

            except Exception as e:
                logger.error(f"❌ Validation failed for {target_curve}: {e}")
                import traceback
                traceback.print_exc()

        # Save comprehensive results
        self._save_validation_results(all_validation_results)

        # Generate summary report
        self._generate_validation_report(all_validation_results)

        logger.info("🎉 Comprehensive Model Validation Complete!")

    def _save_validation_results(self, results: Dict):
        """Save comprehensive validation results"""
        results_file = self.results_dir / "comprehensive_validation_results.json"

        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"💾 Validation results saved: {results_file}")

    def _generate_validation_report(self, results: Dict):
        """Generate comprehensive validation report"""
        report_file = self.results_dir / "validation_report.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Comprehensive Model Validation Report\n\n")
            f.write(f"**Generated**: {__import__('datetime').datetime.now()}\n")
            f.write(f"**Models Validated**: {len(results)}\n")
            f.write(f"**Validation Method**: 5-Fold Cross-Validation\n\n")

            f.write("## Executive Summary\n\n")

            # Performance summary table
            f.write("| Model | R² Norm (95% CI) | R² Phys (95% CI) | Quality | Overfitting | Uncertainty |\n")
            f.write("|-------|------------------|------------------|---------|-------------|-------------|\n")

            for curve, data in results.items():
                cv = data['cross_validation']['summary_statistics']['r2']
                r2_mean = cv['mean']
                ci_lower = cv['ci_lower']
                ci_upper = cv['ci_upper']

                # Physical unit R² if available
                r2_phys_str = "N/A"
                if 'r2_phys' in data['cross_validation']['summary_statistics']:
                    cv_phys = data['cross_validation']['summary_statistics']['r2_phys']
                    r2_phys_str = f"{cv_phys['mean']:.3f} [{cv_phys['ci_lower']:.3f}, {cv_phys['ci_upper']:.3f}]"

                # Quality assessment
                if r2_mean > 0.8:
                    quality = "🟢 Excellent"
                elif r2_mean > 0.6:
                    quality = "🟡 Good"
                elif r2_mean > 0.3:
                    quality = "🟠 Moderate"
                else:
                    quality = "🔴 Poor"

                # Overfitting assessment
                overfitting = "Unknown"
                if data['overfitting_analysis']:
                    if data['overfitting_analysis']['overfitting_detected']:
                        overfitting = "🟠 Detected"
                    else:
                        overfitting = "🟢 None"

                # Uncertainty assessment
                uncertainty = "Unknown"
                if data['uncertainty_analysis']:
                    avg_uncertainty = data['uncertainty_analysis']['average_uncertainty_ratio']
                    if avg_uncertainty > 0.2:
                        uncertainty = "🔴 High"
                    elif avg_uncertainty > 0.1:
                        uncertainty = "🟠 Medium"
                    else:
                        uncertainty = "🟢 Low"

                f.write(f"| {curve} | {r2_mean:.3f} [{ci_lower:.3f}, {ci_upper:.3f}] | {r2_phys_str} | {quality} | {overfitting} | {uncertainty} |\n")

            f.write("\n## Detailed Analysis\n\n")

            for curve, data in results.items():
                f.write(f"### {curve} Prediction Model\n\n")

                cv_stats = data['cross_validation']['summary_statistics']

                f.write("#### Cross-Validation Results\n")
                f.write(f"- **R² Score (Normalized)**: {cv_stats['r2']['mean']:.4f} ± {cv_stats['r2']['std']:.4f}\n")
                f.write(f"- **95% Confidence Interval**: [{cv_stats['r2']['ci_lower']:.4f}, {cv_stats['r2']['ci_upper']:.4f}]\n")
                f.write(f"- **MAE (Normalized)**: {cv_stats['mae']['mean']:.4f} ± {cv_stats['mae']['std']:.4f}\n")
                f.write(f"- **RMSE (Normalized)**: {cv_stats['rmse']['mean']:.4f} ± {cv_stats['rmse']['std']:.4f}\n")

                # Add physical unit metrics if available
                if 'r2_phys' in cv_stats:
                    f.write(f"- **R² Score (Physical Units)**: {cv_stats['r2_phys']['mean']:.4f} ± {cv_stats['r2_phys']['std']:.4f}\n")
                    f.write(f"- **95% CI (Physical)**: [{cv_stats['r2_phys']['ci_lower']:.4f}, {cv_stats['r2_phys']['ci_upper']:.4f}]\n")

                if 'mae_phys' in cv_stats:
                    f.write(f"- **MAE (Physical Units)**: {cv_stats['mae_phys']['mean']:.2f} ± {cv_stats['mae_phys']['std']:.2f} μs/ft\n")

                if 'rmse_phys' in cv_stats:
                    f.write(f"- **RMSE (Physical Units)**: {cv_stats['rmse_phys']['mean']:.2f} ± {cv_stats['rmse_phys']['std']:.2f} μs/ft\n")

                # Overfitting analysis
                if data['overfitting_analysis']:
                    oa = data['overfitting_analysis']
                    f.write(f"\n#### Overfitting Analysis\n")
                    f.write(f"- **Overfitting Score**: {oa['overfitting_score']:.1f}%\n")
                    f.write(f"- **Early Stopping**: {'Yes' if oa['early_stopping_triggered'] else 'No'}\n")
                    f.write(f"- **Training-Validation Gap**: {oa['train_val_gap']:.4f}\n")

                # Uncertainty analysis
                if data['uncertainty_analysis']:
                    ua = data['uncertainty_analysis']
                    f.write(f"\n#### Uncertainty Analysis\n")
                    f.write(f"- **Average Uncertainty Ratio**: {ua['average_uncertainty_ratio']:.4f}\n")
                    f.write(f"- **High Uncertainty Samples**: {ua['high_uncertainty_samples']}/{ua['n_samples_analyzed']}\n")

                f.write("\n")

            f.write("## Recommendations\n\n")

            # Generate specific recommendations
            excellent_models = []
            poor_models = []

            for curve, data in results.items():
                r2_mean = data['cross_validation']['summary_statistics']['r2']['mean']
                if r2_mean > 0.8:
                    excellent_models.append(curve)
                elif r2_mean < 0.3:
                    poor_models.append(curve)

            if excellent_models:
                f.write(f"### Production Ready Models: {', '.join(excellent_models)}\n")
                f.write("- Deploy to production environment\n")
                f.write("- Implement real-time monitoring\n")
                f.write("- Collect feedback for continuous improvement\n\n")

            if poor_models:
                f.write(f"### Models Requiring Improvement: {', '.join(poor_models)}\n")
                f.write("- Increase training dataset size\n")
                f.write("- Implement data augmentation\n")
                f.write("- Try different model architectures\n")
                f.write("- Perform hyperparameter optimization\n\n")

        logger.info(f"📋 Validation report saved: {report_file}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Comprehensive Model Validation")
    parser.add_argument('--models-dir', type=str,
                       default='training/outputs',
                       help='Directory containing trained models (relative to multiwell/)')
    parser.add_argument('--output-dir', type=str,
                       default='validation/outputs',
                       help='Output directory for validation results (relative to multiwell/)')

    args = parser.parse_args()

    config = {
        'models_dir': args.models_dir,
        'output_dir': args.output_dir
    }

    validator = ModelValidator(config)
    validator.run_comprehensive_validation()


if __name__ == "__main__":
    main()
