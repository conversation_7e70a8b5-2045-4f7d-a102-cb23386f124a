# VP (Acoustic Velocity) Prediction Pipeline

## Overview

This is a comprehensive, well-structured VP prediction pipeline that implements a complete three-stage workflow for acoustic velocity prediction using transformer-based deep learning models. The pipeline has been refactored from the original `train_vp_improved.py` to provide better organization, maintainability, and professional-grade functionality.

## Features

### 🏗️ **Three-Stage Architecture**
- **Stage 1 - Training**: Model training with proper checkpointing, loss tracking, and model saving
- **Stage 2 - Validation**: Comprehensive model validation with cross-validation and performance metrics
- **Stage 3 - Prediction**: Model inference with prediction visualization and analysis

### 📁 **Organized Output Structure**
```
initial/
├── train_vp_improved.py (refactored)
├── vp_prediction_outputs/
│   ├── training/          # Training results, models, logs
│   ├── validation/        # Validation metrics, plots
│   └── prediction/        # Prediction results, visualizations
```

### 🔧 **Professional Quality Standards**
- Comprehensive error handling and logging
- Progress indicators for long-running operations
- Model checkpointing and resume capability
- Professional visualization and reporting
- Configuration management system
- Integration with vp_predictor package

## Quick Start

### Run Complete Pipeline
```bash
# Run all three stages with default settings
python train_vp_improved.py

# Run with custom configuration
python train_vp_improved.py --config custom_config.json

# Run with custom output directory
python train_vp_improved.py --output-dir my_vp_results
```

### Run Individual Stages
```bash
# Training only
python train_vp_improved.py --stage training

# Validation only (requires trained model)
python train_vp_improved.py --stage validation --model-path vp_prediction_outputs/training/best_vp_model.pth

# Prediction only (requires trained model)
python train_vp_improved.py --stage prediction --model-path vp_prediction_outputs/training/best_vp_model.pth
```

## Architecture Components

### 🎯 **Core Classes**

#### `VpPredictionPipeline`
- Main orchestrator for the complete workflow
- Manages all three stages and their coordination
- Handles configuration and output directory setup

#### `VpTrainingManager`
- Comprehensive training management
- Data preparation and model creation
- Training loop with progress tracking and early stopping
- Training results visualization and saving

#### `VpValidationManager`
- Cross-validation implementation
- Performance metrics calculation (R², RMSE, MAE)
- Overfitting detection and uncertainty quantification
- Validation reports and diagnostic plots

#### `VpPredictionManager`
- Model loading and inference
- Test data preparation and prediction generation
- Comprehensive result visualization
- Statistical analysis and performance assessment

#### `VpPipelineConfig`
- Centralized configuration management
- Default parameter settings with override capability
- JSON configuration file support

#### `VpDataProcessor`
- Enhanced data processing with quality validation
- Automatic data file detection
- Proper normalization and augmentation
- Missing data handling strategies

### 🔗 **Integration Points**

The pipeline seamlessly integrates with the `vp_predictor` package:
- Uses `MWLT_Vp_Base`, `MWLT_Vp_Small`, `MWLT_Vp_Large` models
- Integrates with `VpDataNormalizer` for consistent data processing
- Utilizes `LASProcessor` for data loading
- Follows multiwell application patterns and standards

## Configuration

### Default Configuration
The pipeline uses a comprehensive default configuration covering:
- Model architecture (type, channels, features)
- Training parameters (learning rate, batch size, epochs)
- Validation settings (CV folds, confidence levels)
- Data processing (curves, augmentation, quality thresholds)
- Output formatting (directories, plots, reports)

### Custom Configuration
Create a JSON configuration file to override defaults:
```json
{
  "model": {
    "type": "large",
    "feature_num": 128
  },
  "training": {
    "batch_size": 16,
    "learning_rate": 5e-5,
    "epochs": 300
  },
  "validation": {
    "cv_folds": 10
  }
}
```

## Output Structure

### Training Stage (`vp_prediction_outputs/training/`)
- `best_vp_model.pth`: Best trained model checkpoint
- `training_history.json`: Complete training metrics history
- `training_results.json`: Final training results and configuration
- `training_progress.png`: Training progress visualization
- `training.log`: Detailed training logs

### Validation Stage (`vp_prediction_outputs/validation/`)
- `cv_results.json`: Cross-validation results and statistics
- `validation_report.json`: Comprehensive validation assessment
- `cv_results.png`: Cross-validation visualization
- `validation.log`: Validation process logs

### Prediction Stage (`vp_prediction_outputs/prediction/`)
- `prediction_report.json`: Prediction performance analysis
- `detailed_predictions.json`: Complete prediction data
- `prediction_results.png`: Comprehensive prediction visualizations
- `prediction.log`: Prediction process logs

### Pipeline Summary
- `pipeline_summary.json`: Complete pipeline execution summary
- `pipeline_config.json`: Configuration used for the run
- `pipeline.log`: Main pipeline execution log

## Performance Metrics

The pipeline tracks comprehensive performance metrics:
- **R² Score**: Coefficient of determination
- **RMSE**: Root Mean Square Error
- **MAE**: Mean Absolute Error
- **MSE**: Mean Square Error
- **Cross-validation statistics**: Mean, std, confidence intervals
- **Statistical tests**: Normality tests for residuals

## Visualization Features

### Training Visualizations
- Training and validation loss curves
- R² score progression
- RMSE progression
- Combined metrics overview

### Validation Visualizations
- Cross-validation box plots
- Performance distribution across folds
- Statistical significance indicators

### Prediction Visualizations
- Predicted vs Actual scatter plots
- Residuals analysis
- Error distribution histograms
- Time series comparisons
- Performance metrics summary

## Error Handling and Logging

- **Comprehensive logging**: Multi-level logging with file and console output
- **Robust error handling**: Graceful failure handling with detailed error messages
- **Progress tracking**: Real-time progress indicators for long operations
- **Resume capability**: Model checkpointing allows training resumption

## Dependencies

### Core Requirements
- PyTorch (deep learning framework)
- NumPy (numerical computations)
- Matplotlib, Seaborn (visualization)
- scikit-learn (metrics and cross-validation)
- SciPy (statistical functions)
- tqdm (progress bars)

### Integration Requirements
- `vp_predictor` package (preferred) or local modules
- HDF5 data files (A1.hdf5, A2.hdf5)

## Usage Examples

### Basic Usage
```python
from train_vp_improved import VpPredictionPipeline

# Initialize and run complete pipeline
pipeline = VpPredictionPipeline()
results = pipeline.run_complete_pipeline()

print(f"Training R²: {results['training']['final_metrics']['best_val_r2']:.4f}")
print(f"Validation R²: {results['validation']['cv_results']['cv_summary']['r2']['mean']:.4f}")
print(f"Test R²: {results['prediction']['prediction_results']['metrics']['r2']:.4f}")
```

### Advanced Configuration
```python
# Custom configuration
config = VpPipelineConfig({
    'model': {'type': 'large'},
    'training': {'epochs': 500, 'batch_size': 16},
    'validation': {'cv_folds': 10}
})

pipeline = VpPredictionPipeline(output_dir='advanced_vp_results')
pipeline.config = config
results = pipeline.run_complete_pipeline()
```

## Best Practices

1. **Data Quality**: Ensure A1.hdf5 and A2.hdf5 files are available and properly formatted
2. **Configuration**: Use custom configurations for specific requirements
3. **Monitoring**: Check logs regularly during long training runs
4. **Validation**: Always run validation stage to assess model reliability
5. **Documentation**: Review generated reports for performance insights

## Troubleshooting

### Common Issues
- **Data files not found**: Ensure A1.hdf5 and A2.hdf5 are in accessible locations
- **Memory issues**: Reduce batch size or use smaller model variant
- **Poor performance**: Check data quality and consider hyperparameter tuning
- **Import errors**: Verify vp_predictor package installation or use local modules

### Performance Optimization
- Use GPU when available (automatically detected)
- Adjust batch size based on available memory
- Use appropriate model size for your data complexity
- Monitor training progress and use early stopping

This refactored VP prediction pipeline provides a robust, maintainable, and extensible foundation for acoustic velocity prediction while maintaining full compatibility with the broader MWLT system architecture.
